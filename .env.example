# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=moyenne_bac
DB_USER=postgres
DB_PASSWORD=your_password_here

# JWT Secret for Authentication
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Session Secret
SESSION_SECRET=your_session_secret_here_also_make_it_random

# Cloudinary Configuration (Free Cloud Storage)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Admin Default Credentials (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE_MB=50
MAX_FILES_PER_UPLOAD=10
