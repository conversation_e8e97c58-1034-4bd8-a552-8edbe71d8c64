<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Administration Bac Informatique</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
        }

        .login-container {
            background: var(--card-background);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .subtitle {
            color: var(--text-secondary);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            background: var(--card-background);
            color: var(--text-primary);
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-primary:disabled {
            background-color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .links {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
        }

        .links a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            margin-left: 10px;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-credentials {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .demo-credentials h4 {
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .demo-credentials p {
            margin: 4px 0;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔐</div>
        <h1 class="title">Administration</h1>
        <p class="subtitle">Bac Informatique Tunisie</p>

        <div class="demo-credentials">
            <h4>🧪 Identifiants de démonstration</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Mot de passe:</strong> admin123456</p>
        </div>

        <form id="login-form">
            <div class="form-group">
                <label for="email">📧 Email</label>
                <input type="email" id="email" class="form-control" required 
                       placeholder="<EMAIL>" value="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="password">🔑 Mot de passe</label>
                <input type="password" id="password" class="form-control" required 
                       placeholder="Votre mot de passe" value="admin123456">
            </div>

            <button type="submit" class="btn btn-primary" id="login-btn">
                Se connecter
                <span class="loading" id="loading">
                    <span class="spinner"></span>
                </span>
            </button>
        </form>

        <div id="alert-container"></div>

        <div class="links">
            <a href="simple.html">🧮 Calculateur</a>
            <a href="exams.html">📚 Examens</a>
        </div>
    </div>

    <script>
        // Check if already logged in
        async function checkAuth() {
            try {
                const response = await fetch('/api/auth/check', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    // Already logged in, redirect to admin
                    window.location.href = '/admin.html';
                }
            } catch (error) {
                // Not logged in, stay on login page
            }
        }

        // Login form handler
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            const loading = document.getElementById('loading');
            
            // Show loading state
            loginBtn.disabled = true;
            loading.style.display = 'inline-block';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ Connexion réussie ! Redirection...', 'success');
                    
                    // Store token in localStorage as backup
                    localStorage.setItem('auth_token', result.token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    
                    // Redirect to admin page
                    setTimeout(() => {
                        window.location.href = '/admin.html';
                    }, 1000);
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                showAlert('❌ Erreur de connexion au serveur', 'error');
            } finally {
                // Hide loading state
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });

        // Show alert function
        function showAlert(message, type) {
            const container = document.getElementById('alert-container');
            container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Check authentication on page load
        checkAuth();

        // Auto-fill demo credentials button
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab' && e.shiftKey) {
                // Shift+Tab to auto-fill demo credentials
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = 'admin123456';
            }
        });
    </script>
</body>
</html>
