{"name": "moyenne-bac", "private": true, "version": "0.0.0", "type": "commonjs", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "server": "node server.js", "start": "concurrently \"npm run server\" \"npm run dev\"", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "deploy:setup": "npm run db:migrate && npm run db:seed", "setup:cloudinary": "node setup-cloudinary.js"}, "dependencies": {"concurrently": "^8.2.2", "cors": "^2.8.5", "express": "^4.21.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "cloudinary": "^1.41.0", "multer-storage-cloudinary": "^4.0.0", "express-session": "^1.17.3", "connect-pg-simple": "^9.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5"}}