# Production Environment Variables for Railway/Render/Vercel
# Copy these to your hosting platform's environment variables section

# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration (Your Neon DB - already configured)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# JWT Secret for Authentication (CHANGE THIS IN PRODUCTION!)
JWT_SECRET=super_secret_jwt_key_for_bac_informatique_2024_make_it_very_long_and_random_PRODUCTION

# Session Secret (CHANGE THIS IN PRODUCTION!)
SESSION_SECRET=session_secret_for_bac_informatique_also_very_random_and_secure_PRODUCTION

# Cloudinary Configuration (Get from your Cloudinary dashboard)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

# Admin Default Credentials (CHANGE PASSWORD IN PRODUCTION!)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_production_password_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE_MB=50
MAX_FILES_PER_UPLOAD=10

# Frontend URL (will be your deployed domain)
FRONTEND_URL=https://your-app.railway.app
